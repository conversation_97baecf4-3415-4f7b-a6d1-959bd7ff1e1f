const http = require('http');
const https = require('https');
const url = require('url');

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    // 设置CORS头，允许跨域
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    res.setHeader('Content-Type', 'application/json; charset=utf-8');

    // 处理OPTIONS预检请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // 解析URL
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const query = parsedUrl.query;

    // 处理主API请求
    if (req.method === 'GET' && pathname === '/api') {
        console.log('收到API请求，开始调用昆鹏360接口...');
        
        // 目标API URL
        const apiUrl = 'https://www.kunpeng360.com/CustomerQuery/Query?t=05580002&barcode=9b0e1005eb09497ea5ae9b4b8114c1e4';
        
        // 发起HTTPS请求
        const request = https.get(apiUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        }, (apiRes) => {
            console.log(`API响应状态码: ${apiRes.statusCode}`);
            console.log(`API响应头:`, apiRes.headers);
            
            let data = '';
            
            // 接收数据
            apiRes.on('data', (chunk) => {
                data += chunk;
            });
            
            // 数据接收完成
            apiRes.on('end', () => {
                console.log('API返回的原始数据:');
                console.log(data);
                
                // 尝试解析JSON
                let responseData;
                try {
                    responseData = JSON.parse(data);
                    console.log('解析后的JSON数据:');
                    console.log(JSON.stringify(responseData, null, 2));
                } catch (e) {
                    console.log('返回的不是JSON格式，作为文本处理');
                    responseData = {
                        success: true,
                        data: data,
                        type: 'text',
                        message: '成功获取数据'
                    };
                }

                // 如果API返回的数据中没有EntName和CarouselImages，添加测试数据
                if (responseData && typeof responseData === 'object') {
                    if (!responseData.EntName) {
                        responseData.EntName = '安徽美誉制药有限公司';
                    }
                    if (!responseData.EntCode) {
                        responseData.EntCode = '05580002';
                    }
                    if (!responseData.CarouselImages || !Array.isArray(responseData.CarouselImages)) {
                        responseData.CarouselImages = [
                            '688dd2645545dbf1f62328c5',
                            '688dd26b5545dbf1f62328c7',
                            '688dd2745545dbf1f62328ca',
                            '688dd28f5545dbf1f62328cd',
                            '688dd2985545dbf1f62328d0'
                        ];
                    }

                    // 添加企业介绍数据
                    if (!responseData.EntPublicize || typeof responseData.EntPublicize !== 'object') {
                        responseData.EntPublicize = {
                            '企业介绍': '688dd2445545dbf1f62328c4'
                        };
                    }

                    // 添加测试的 LifeCycleNodes 数据
                    if (!responseData.LifeCycleNodes || !Array.isArray(responseData.LifeCycleNodes)) {
                        responseData.LifeCycleNodes = [
                            {
                                "NodeName": "产品信息",
                                "Properties": [
                                    {"PropertyName": "产品信息", "PropertyType": 1},
                                    {"PropertyName": "批号", "PropertyType": 1},
                                    {"PropertyName": "产地", "PropertyType": 1},
                                    {"PropertyName": "净重", "PropertyType": 1},
                                    {"PropertyName": "规格", "PropertyType": 1},
                                    {"PropertyName": "生产日期", "PropertyType": 1},
                                    {"PropertyName": "保质期", "PropertyType": 1},
                                    {"PropertyName": "生产许可证", "PropertyType": 1},
                                    {"PropertyName": "执行标准", "PropertyType": 1},
                                    {"PropertyName": "贮藏", "PropertyType": 1},
                                    {"PropertyName": "生产企业", "PropertyType": 1},
                                    {"PropertyName": "生产地址", "PropertyType": 1}
                                ]
                            },
                            {
                                "NodeName": "检验信息",
                                "Properties": [
                                    {"PropertyName": "原药材检测", "PropertyType": 7},
                                    {"PropertyName": "成品检测", "PropertyType": 7},
                                    {"PropertyName": "检测报告", "PropertyType": 1}
                                ]
                            },
                            {
                                "NodeName": "基地信息",
                                "Properties": [
                                    {"PropertyName": "种源信息", "PropertyType": 1},
                                    {"PropertyName": "环境信息", "PropertyType": 1},
                                    {"PropertyName": "田间管理", "PropertyType": 1},
                                    {"PropertyName": "采收信息", "PropertyType": 1},
                                    {"PropertyName": "产地初加工", "PropertyType": 1}
                                ]
                            }
                        ];
                    }

                    // 添加测试的 LifeCycles 数据
                    if (!responseData.LifeCycles || typeof responseData.LifeCycles !== 'object') {
                        responseData.LifeCycles = {
                            // 产品信息节点数据
                            "产品信息_开始时间": "2025.08.01",
                            "产品信息_产品信息": "芡实",
                            "产品信息_批号": "25080101A",
                            "产品信息_产地": "广东省肇庆市",
                            "产品信息_净重": "119.3",
                            "产品信息_规格": "净制",
                            "产品信息_生产日期": "2025.08.01",
                            "产品信息_保质期": "24个月",
                            "产品信息_生产许可证": "皖20160093",
                            "产品信息_执行标准": "《中国药典》2020版一部及四部",
                            "产品信息_贮藏": "置通风干燥处，防蛀。",
                            "产品信息_生产企业": "安徽美誉制药有限公司",
                            "产品信息_生产地址": "安徽省亳州市谯城经济开发区张良路东侧、亳芍路南侧",

                            // 检验信息节点数据
                            "检验信息_开始时间": "2025.07.23",
                            "检验信息_原药材检测": "688dd2645545dbf1f62328c5",
                            "检验信息_成品检测": "688dd26b5545dbf1f62328c7",
                            "检验信息_检测报告": "所有检测项目均符合标准要求",

                            // 基地信息节点数据
                            "基地信息_开始时间": "2025.07.20",
                            "基地信息_种源信息": "睡莲科植物芡Euryale ferox Salisb.的干燥成熟种仁",
                            "基地信息_环境信息": "生在池塘、湖沼中",
                            "基地信息_田间管理": "4月份种植，种子育苗，复合肥、腐熟有机肥、尿素",
                            "基地信息_采收信息": "11月份采收，干燥成熟种仁、人工",
                            "基地信息_产地初加工": "秋末冬初采收成熟果实，除去果皮，取出种子，洗净，再除去硬壳（外种皮），晒干"
                        };
                    }
                }
                
                // 返回给前端
                res.writeHead(200);
                res.end(JSON.stringify({
                    success: true,
                    statusCode: apiRes.statusCode,
                    headers: apiRes.headers,
                    data: responseData,
                    rawData: data
                }, null, 2));
            });
        });
        
        // 处理请求错误
        request.on('error', (error) => {
            console.error('请求API时发生错误:', error);
            res.writeHead(500);
            res.end(JSON.stringify({
                success: false,
                error: error.message,
                message: '调用API失败'
            }, null, 2));
        });
        
        // 设置请求超时
        request.setTimeout(10000, () => {
            console.error('请求超时');
            request.destroy();
            res.writeHead(408);
            res.end(JSON.stringify({
                success: false,
                error: 'Request timeout',
                message: '请求超时'
            }, null, 2));
        });

    } else if (req.method === 'GET' && pathname === '/api/company-intro') {
        // 处理企业介绍接口
        const t = query.t || '05580002';
        const docId = query.docId;

        if (!docId) {
            res.writeHead(400);
            res.end(JSON.stringify({
                success: false,
                error: 'Missing docId parameter',
                message: '缺少docId参数'
            }));
            return;
        }

        console.log(`收到企业介绍请求，t=${t}, docId=${docId}`);

        // 目标企业介绍API URL
        const introApiUrl = `https://www.kunpeng360.com/CustomerQuery/DocHtml?t=${t}&docId=${docId}`;
        console.log('调用企业介绍接口:', introApiUrl);

        // 发起HTTPS请求
        const introRequest = https.get(introApiUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        }, (introApiRes) => {
            console.log(`企业介绍API响应状态码: ${introApiRes.statusCode}`);

            let data = '';

            // 接收数据
            introApiRes.on('data', (chunk) => {
                data += chunk;
            });

            // 数据接收完成
            introApiRes.on('end', () => {
                console.log('企业介绍API返回的原始数据:');
                console.log(data);

                // 设置响应头为HTML
                res.setHeader('Content-Type', 'text/html; charset=utf-8');
                res.writeHead(200);
                res.end(data);
            });
        });

        // 处理请求错误
        introRequest.on('error', (error) => {
            console.error('请求企业介绍API时发生错误:', error);
            res.writeHead(500);
            res.end(`<p>企业介绍加载失败: ${error.message}</p>`);
        });

        // 设置请求超时
        introRequest.setTimeout(10000, () => {
            console.error('企业介绍请求超时');
            introRequest.destroy();
            res.writeHead(408);
            res.end('<p>企业介绍请求超时</p>');
        });

    } else if (req.method === 'GET' && pathname === '/') {
        // 返回简单的测试页面
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
        res.writeHead(200);
        res.end(`
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>API代理服务器</title>
            </head>
            <body>
                <h1>API代理服务器运行中</h1>
                <p>访问 <a href="/api">/api</a> 来调用昆鹏360接口</p>
                <button onclick="testAPI()">测试API调用</button>
                <div id="result"></div>
                
                <script>
                async function testAPI() {
                    try {
                        const response = await fetch('/api');
                        const data = await response.json();
                        document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                        console.log('API返回数据:', data);
                    } catch (error) {
                        console.error('调用失败:', error);
                        document.getElementById('result').innerHTML = '<p style="color: red;">调用失败: ' + error.message + '</p>';
                    }
                }
                </script>
            </body>
            </html>
        `);
    } else {
        // 404错误
        res.writeHead(404);
        res.end(JSON.stringify({
            success: false,
            error: 'Not Found',
            message: '路径不存在'
        }));
    }
});

// 启动服务器
const PORT = 3000;
server.listen(PORT, () => {
    console.log(`代理服务器已启动，访问地址: http://localhost:${PORT}`);
    console.log(`API接口地址: http://localhost:${PORT}/api`);
    console.log('按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});
