<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业介绍测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .company-intro-content {
            margin-top: 20px;
            padding: 20px;
            border: 2px solid #28a745;
            border-radius: 8px;
            background: #f8fff9;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            color: #155724;
            background: #d4edda;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>企业介绍功能测试</h1>
    
    <div class="test-section">
        <h2>1. 测试主API调用</h2>
        <button class="test-button" onclick="testMainAPI()">调用主API</button>
        <div id="mainApiResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试企业介绍API调用</h2>
        <button class="test-button" onclick="testCompanyIntroAPI()">调用企业介绍API</button>
        <div id="companyIntroResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 企业介绍内容展示</h2>
        <button class="test-button" onclick="loadCompanyIntro()">加载企业介绍</button>
        <div id="companyIntroContent" class="company-intro-content"></div>
    </div>

    <script>
        // 测试主API调用
        async function testMainAPI() {
            const resultDiv = document.getElementById('mainApiResult');
            resultDiv.textContent = '正在调用主API...';
            
            try {
                const response = await fetch('http://localhost:3000/api');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 主API调用成功！\n企业名称: ${data.data.EntName}\n企业代码: ${data.data.EntCode}\n企业介绍docId: ${data.data.EntPublicize?.企业介绍 || '未找到'}`;
                    
                    // 保存数据供其他测试使用
                    window.testData = data.data;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 主API调用失败: ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 主API调用出错: ${error.message}`;
            }
        }

        // 测试企业介绍API调用
        async function testCompanyIntroAPI() {
            const resultDiv = document.getElementById('companyIntroResult');
            
            if (!window.testData || !window.testData.EntPublicize?.企业介绍) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请先调用主API获取docId';
                return;
            }
            
            const entCode = window.testData.EntCode || '05580002';
            const docId = window.testData.EntPublicize.企业介绍;
            
            resultDiv.textContent = `正在调用企业介绍API...\nentCode: ${entCode}\ndocId: ${docId}`;
            
            try {
                const response = await fetch(`http://localhost:3000/api/company-intro?t=${entCode}&docId=${docId}`);
                const htmlContent = await response.text();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 企业介绍API调用成功！\n返回内容长度: ${htmlContent.length} 字符\n内容预览: ${htmlContent.substring(0, 200)}...`;
                    
                    // 保存HTML内容供展示使用
                    window.companyIntroHTML = htmlContent;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 企业介绍API调用失败: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 企业介绍API调用出错: ${error.message}`;
            }
        }

        // 加载企业介绍内容
        async function loadCompanyIntro() {
            const contentDiv = document.getElementById('companyIntroContent');
            
            if (!window.companyIntroHTML) {
                contentDiv.innerHTML = '<p style="color: red;">❌ 请先调用企业介绍API获取内容</p>';
                return;
            }
            
            // 显示HTML内容
            contentDiv.innerHTML = window.companyIntroHTML;
            
            // 处理图片点击事件
            const images = contentDiv.querySelectorAll('img');
            images.forEach(img => {
                img.style.cursor = 'pointer';
                img.onclick = function() {
                    // 如果图片有onclick属性，提取showPreview的参数
                    if (this.hasAttribute('onclick')) {
                        const onclickValue = this.getAttribute('onclick');
                        if (onclickValue.includes('showPreview')) {
                            const match = onclickValue.match(/showPreview\('([^']+)',\s*true\)/);
                            if (match) {
                                const imageUrl = match[1];
                                const fullImageUrl = `https://www.kunpeng360.com${imageUrl}`;
                                alert(`图片URL: ${fullImageUrl}\n\n在实际应用中，这里会打开图片弹窗。`);
                                // 在新窗口中打开图片
                                window.open(fullImageUrl, '_blank');
                            }
                        }
                    } else {
                        alert(`图片URL: ${this.src}\n\n在实际应用中，这里会打开图片弹窗。`);
                        window.open(this.src, '_blank');
                    }
                };
            });
            
            console.log('✅ 企业介绍内容已加载，图片点击事件已设置');
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            console.log('企业介绍测试页面已加载');
        });
    </script>
</body>
</html>
